/// Model for tracking exercise progression data
class ExerciseProgression {
  final String exerciseId;
  final String exerciseName;
  final ExerciseType type;
  final int currentReps;
  final double currentWeight; // in kg (converted internally)
  final int currentDuration; // in seconds
  final int currentSets;
  final int consecutiveSuccesses; // successful completions in a row
  final int consecutiveFailures; // failed completions in a row
  final DateTime lastUpdated;
  final int weekNumber; // for deload tracking
  final double metValue; // for calorie calculation
  final List<ProgressionHistory> history;

  ExerciseProgression({
    required this.exerciseId,
    required this.exerciseName,
    required this.type,
    required this.currentReps,
    required this.currentWeight,
    required this.currentDuration,
    required this.currentSets,
    this.consecutiveSuccesses = 0,
    this.consecutiveFailures = 0,
    required this.lastUpdated,
    this.weekNumber = 1,
    required this.metValue,
    this.history = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'exerciseId': exerciseId,
      'exerciseName': exerciseName,
      'type': type.toString(),
      'currentReps': currentReps,
      'currentWeight': currentWeight,
      'currentDuration': currentDuration,
      'currentSets': currentSets,
      'consecutiveSuccesses': consecutiveSuccesses,
      'consecutiveFailures': consecutiveFailures,
      'lastUpdated': lastUpdated.toIso8601String(),
      'weekNumber': weekNumber,
      'metValue': metValue,
      'history': history.map((h) => h.toJson()).toList(),
    };
  }

  factory ExerciseProgression.fromJson(Map<String, dynamic> json) {
    return ExerciseProgression(
      exerciseId: json['exerciseId'],
      exerciseName: json['exerciseName'],
      type: ExerciseType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ExerciseType.bodyweight,
      ),
      currentReps: json['currentReps'],
      currentWeight: json['currentWeight']?.toDouble() ?? 0.0,
      currentDuration: json['currentDuration'],
      currentSets: json['currentSets'],
      consecutiveSuccesses: json['consecutiveSuccesses'] ?? 0,
      consecutiveFailures: json['consecutiveFailures'] ?? 0,
      lastUpdated: DateTime.parse(json['lastUpdated']),
      weekNumber: json['weekNumber'] ?? 1,
      metValue: json['metValue']?.toDouble() ?? 5.0,
      history: (json['history'] as List<dynamic>?)
          ?.map((h) => ProgressionHistory.fromJson(h))
          .toList() ?? [],
    );
  }

  ExerciseProgression copyWith({
    String? exerciseId,
    String? exerciseName,
    ExerciseType? type,
    int? currentReps,
    double? currentWeight,
    int? currentDuration,
    int? currentSets,
    int? consecutiveSuccesses,
    int? consecutiveFailures,
    DateTime? lastUpdated,
    int? weekNumber,
    double? metValue,
    List<ProgressionHistory>? history,
  }) {
    return ExerciseProgression(
      exerciseId: exerciseId ?? this.exerciseId,
      exerciseName: exerciseName ?? this.exerciseName,
      type: type ?? this.type,
      currentReps: currentReps ?? this.currentReps,
      currentWeight: currentWeight ?? this.currentWeight,
      currentDuration: currentDuration ?? this.currentDuration,
      currentSets: currentSets ?? this.currentSets,
      consecutiveSuccesses: consecutiveSuccesses ?? this.consecutiveSuccesses,
      consecutiveFailures: consecutiveFailures ?? this.consecutiveFailures,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      weekNumber: weekNumber ?? this.weekNumber,
      metValue: metValue ?? this.metValue,
      history: history ?? this.history,
    );
  }

  /// Calculate calories burned for this exercise
  double calculateCaloriesBurned(double userWeightKg, int durationMinutes) {
    // Calories = MET × weight(kg) × duration(hours)
    return metValue * userWeightKg * (durationMinutes / 60.0);
  }

  /// Check if this exercise is due for a deload (every 5th week)
  bool get isDeloadWeek => weekNumber % 5 == 0;

  /// Get the progression status text
  String get progressionStatus {
    if (consecutiveFailures >= 2) {
      return 'Struggling - Consider reducing difficulty';
    } else if (consecutiveSuccesses >= 3) {
      return 'Ready for progression';
    } else if (isDeloadWeek) {
      return 'Deload week - Reduced volume';
    } else {
      return 'Maintaining current level';
    }
  }
}

/// Types of exercises for different progression rules
enum ExerciseType {
  bodyweight,  // Push-ups, squats, etc.
  dumbbell,    // Dumbbell exercises
  timedHold,   // Planks, wall sits, etc.
  cardio,      // Running, cycling, etc.
}

/// History entry for tracking progression changes
class ProgressionHistory {
  final DateTime date;
  final String changeType; // 'progression', 'deload', 'failure'
  final String description;
  final int? oldReps;
  final int? newReps;
  final double? oldWeight;
  final double? newWeight;
  final int? oldDuration;
  final int? newDuration;

  ProgressionHistory({
    required this.date,
    required this.changeType,
    required this.description,
    this.oldReps,
    this.newReps,
    this.oldWeight,
    this.newWeight,
    this.oldDuration,
    this.newDuration,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'changeType': changeType,
      'description': description,
      'oldReps': oldReps,
      'newReps': newReps,
      'oldWeight': oldWeight,
      'newWeight': newWeight,
      'oldDuration': oldDuration,
      'newDuration': newDuration,
    };
  }

  factory ProgressionHistory.fromJson(Map<String, dynamic> json) {
    return ProgressionHistory(
      date: DateTime.parse(json['date']),
      changeType: json['changeType'],
      description: json['description'],
      oldReps: json['oldReps'],
      newReps: json['newReps'],
      oldWeight: json['oldWeight']?.toDouble(),
      newWeight: json['newWeight']?.toDouble(),
      oldDuration: json['oldDuration'],
      newDuration: json['newDuration'],
    );
  }

  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}';
  }
}

/// Default MET values for common exercises
class ExerciseMETValues {
  static const Map<String, double> values = {
    // Bodyweight exercises
    'push-ups': 8.0,
    'squats': 5.0,
    'lunges': 4.0,
    'burpees': 8.0,
    'jumping jacks': 7.0,
    'mountain climbers': 8.0,
    
    // Strength exercises
    'dumbbell press': 6.0,
    'dumbbell rows': 6.0,
    'dumbbell curls': 3.0,
    'dumbbell squats': 6.0,
    'deadlifts': 6.0,
    
    // Timed holds
    'plank': 3.5,
    'wall sit': 4.0,
    'side plank': 3.5,
    
    // Default fallback
    'default': 5.0,
  };

  static double getMETValue(String exerciseName) {
    final key = exerciseName.toLowerCase();
    return values[key] ?? values['default']!;
  }
}

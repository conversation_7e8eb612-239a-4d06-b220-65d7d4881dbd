import '../models/user_profile.dart';
import 'local_storage.dart';

class DashboardStats {
  final double currentWeight;
  final double weightChange;
  final int streakDays;
  final String todayWorkoutName;
  final int todayWorkoutDuration;
  final double totalVolumeThisWeek;
  final int caloriesBurned;
  final int workoutsCompleted;
  final String bestLift;
  final double bestLiftWeight;

  DashboardStats({
    required this.currentWeight,
    required this.weightChange,
    required this.streakDays,
    required this.todayWorkoutName,
    required this.todayWorkoutDuration,
    required this.totalVolumeThisWeek,
    required this.caloriesBurned,
    required this.workoutsCompleted,
    required this.bestLift,
    required this.bestLiftWeight,
  });
}

class DashboardService {
  final LocalStorageService _localStorage = LocalStorageService();

  Future<DashboardStats> getDashboardStats() async {
    try {
      final profile = await _localStorage.loadUserProfile();
      
      // For now, we'll return mock data since we don't have workout/weight tracking implemented yet
      // In a real app, this would fetch from local storage or a database
      return _getMockDashboardStats(profile);
    } catch (e) {
      // Return default stats if there's an error
      return _getDefaultStats();
    }
  }

  DashboardStats _getMockDashboardStats(UserProfile? profile) {
    final currentWeight = profile?.weight ?? 70.0;
    
    return DashboardStats(
      currentWeight: currentWeight,
      weightChange: -0.8, // Mock weight loss
      streakDays: 3,
      todayWorkoutName: "Upper Body Strength",
      todayWorkoutDuration: 45,
      totalVolumeThisWeek: 12500.0,
      caloriesBurned: 450,
      workoutsCompleted: 4,
      bestLift: "Squat",
      bestLiftWeight: 102.0, // 225 lbs ≈ 102 kg
    );
  }

  DashboardStats _getDefaultStats() {
    return DashboardStats(
      currentWeight: 70.0,
      weightChange: 0.0,
      streakDays: 0,
      todayWorkoutName: "Get Started",
      todayWorkoutDuration: 30,
      totalVolumeThisWeek: 0.0,
      caloriesBurned: 0,
      workoutsCompleted: 0,
      bestLift: "None",
      bestLiftWeight: 0.0,
    );
  }

  String getWeightChangeText(double change) {
    if (change > 0) {
      return "+${change.toStringAsFixed(1)} kg";
    } else if (change < 0) {
      return "${change.toStringAsFixed(1)} kg";
    } else {
      return "No change";
    }
  }

  String getWeightChangeDescription(double change) {
    if (change > 0) {
      return "gained since last week";
    } else if (change < 0) {
      return "lost since last week";
    } else {
      return "from last week";
    }
  }

  String getStreakText(int days) {
    if (days == 0) return "Start your streak!";
    if (days == 1) return "1 day";
    if (days < 7) return "$days days";
    
    final weeks = days ~/ 7;
    final remainingDays = days % 7;
    
    if (weeks == 1 && remainingDays == 0) return "1 week";
    if (remainingDays == 0) return "$weeks weeks";
    if (weeks == 1) return "1 week, $remainingDays days";
    return "$weeks weeks, $remainingDays days";
  }

  String formatVolume(double volume) {
    if (volume >= 1000) {
      return "${(volume / 1000).toStringAsFixed(1)}k kg";
    }
    return "${volume.toStringAsFixed(0)} kg";
  }

  String formatCalories(int calories) {
    return "$calories cal";
  }

  String getBestLiftText(String exercise, double weight) {
    if (weight == 0) return "No lifts yet";
    return "$exercise: ${weight.toStringAsFixed(0)} kg";
  }
}

import 'package:flutter/material.dart';
import 'package:personal_training_app/screens/splash_screen.dart';
import 'package:personal_training_app/screens/onboarding_screen.dart';
import 'package:personal_training_app/screens/home_screen.dart';
import 'package:personal_training_app/screens/workout_screen.dart';
import 'package:personal_training_app/screens/history_screen.dart';
import 'package:personal_training_app/screens/profile_screen.dart';
import 'package:personal_training_app/service_locator.dart';
import 'package:personal_training_app/services/local_storage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await LocalStorageService.init();
  setupLocator();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Personal Trainer',
      theme: ThemeData(
        brightness: Brightness.dark,
        primaryColor: const Color(0xFF00ff00),
        scaffoldBackgroundColor: Colors.black,
        colorScheme: const ColorScheme.dark(
          primary: Color(0xFF00ff00),
          secondary: Color(0xFF00ff00),
          surface: Color(0xFF1a1a1a),
          onPrimary: Colors.black,
          onSecondary: Colors.black,
          onSurface: Colors.white,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF00ff00),
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        inputDecorationTheme: const InputDecorationTheme(
          filled: true,
          fillColor: Color(0xFF1a1a1a),
          labelStyle: TextStyle(color: Color(0xFFa3a3a3)),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xFF1a1a1a)),
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xFF00ff00)),
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => SplashScreen(),
        '/onboarding': (context) => OnboardingScreen(),
        '/home': (context) => HomeScreen(),
        '/workout': (context) => WorkoutScreen(),
        '/history': (context) => HistoryScreen(),
        '/profile': (context) => ProfileScreen(),
      },
    );
  }
}
import 'dart:convert';
import '../models/exercise.dart';
import '../models/exercise_progression.dart';
import '../models/user_profile.dart';
import 'local_storage.dart';
import 'week_tracking_service.dart';

/// Service for managing exercise progression logic
class ProgressionService {
  static const String _progressionKey = 'exercise_progressions';

  final LocalStorageService _storage = LocalStorageService();
  final WeekTrackingService _weekService = WeekTrackingService();

  /// Load all exercise progressions from storage
  Future<Map<String, ExerciseProgression>> loadProgressions() async {
    try {
      final data = await _storage.loadData(_progressionKey);
      if (data == null) return {};

      final Map<String, dynamic> jsonMap = jsonDecode(data);
      final Map<String, ExerciseProgression> progressions = {};

      for (final entry in jsonMap.entries) {
        progressions[entry.key] = ExerciseProgression.fromJson(entry.value);
      }

      return progressions;
    } catch (e) {
      print('Error loading progressions: $e');
      return {};
    }
  }

  /// Save exercise progressions to storage
  Future<void> saveProgressions(Map<String, ExerciseProgression> progressions) async {
    try {
      final Map<String, dynamic> jsonMap = {};

      for (final entry in progressions.entries) {
        jsonMap[entry.key] = entry.value.toJson();
      }

      await _storage.saveData(_progressionKey, jsonEncode(jsonMap));
    } catch (e) {
      print('Error saving progressions: $e');
    }
  }

  /// Initialize progression data for a new exercise
  ExerciseProgression initializeProgression(Exercise exercise) {
    final type = _determineExerciseType(exercise);
    final metValue = ExerciseMETValues.getMETValue(exercise.name);

    return ExerciseProgression(
      exerciseId: exercise.id,
      exerciseName: exercise.name,
      type: type,
      currentReps: exercise.reps,
      currentWeight: exercise.targetWeight ?? 0.0,
      currentDuration: exercise.duration ?? 0,
      currentSets: exercise.sets,
      lastUpdated: DateTime.now(),
      metValue: metValue,
    );
  }

  /// Apply progression logic after workout completion
  Future<ExerciseProgression> applyProgression({
    required ExerciseProgression progression,
    required bool allSetsCompleted,
    required UserProfile userProfile,
  }) async {
    final now = DateTime.now();
    final newHistory = List<ProgressionHistory>.from(progression.history);

    // Check if it's a deload week using the week tracking service
    final isDeloadWeek = await _weekService.isDeloadWeek();
    if (isDeloadWeek) {
      return _applyDeload(progression, newHistory, now);
    }

    // Apply progression based on completion status
    if (allSetsCompleted) {
      return _applySuccessProgression(progression, newHistory, now, userProfile);
    } else {
      return _applyFailureProgression(progression, newHistory, now);
    }
  }

  /// Apply deload logic (every 5th week)
  ExerciseProgression _applyDeload(
    ExerciseProgression progression,
    List<ProgressionHistory> history,
    DateTime now,
  ) {
    int newReps = progression.currentReps;
    double newWeight = progression.currentWeight;
    int newDuration = progression.currentDuration;
    String description = '';

    switch (progression.type) {
      case ExerciseType.bodyweight:
      case ExerciseType.timedHold:
        // Reduce reps/duration by 20%
        if (progression.type == ExerciseType.bodyweight) {
          newReps = (progression.currentReps * 0.8).round();
          description = 'Deload: Reduced reps from ${progression.currentReps} to $newReps';
        } else {
          newDuration = (progression.currentDuration * 0.8).round();
          description = 'Deload: Reduced duration from ${progression.currentDuration}s to ${newDuration}s';
        }
        break;
      case ExerciseType.dumbbell:
        // Reduce weight by 10%
        newWeight = progression.currentWeight * 0.9;
        description = 'Deload: Reduced weight from ${progression.currentWeight.toStringAsFixed(1)}kg to ${newWeight.toStringAsFixed(1)}kg';
        break;
      case ExerciseType.cardio:
        // Reduce duration by 15%
        newDuration = (progression.currentDuration * 0.85).round();
        description = 'Deload: Reduced duration from ${progression.currentDuration}s to ${newDuration}s';
        break;
    }

    history.add(ProgressionHistory(
      date: now,
      changeType: 'deload',
      description: description,
      oldReps: progression.currentReps,
      newReps: newReps,
      oldWeight: progression.currentWeight,
      newWeight: newWeight,
      oldDuration: progression.currentDuration,
      newDuration: newDuration,
    ));

    return progression.copyWith(
      currentReps: newReps,
      currentWeight: newWeight,
      currentDuration: newDuration,
      consecutiveSuccesses: 0,
      consecutiveFailures: 0,
      lastUpdated: now,
      weekNumber: progression.weekNumber + 1,
      history: history,
    );
  }

  /// Apply progression for successful completion
  ExerciseProgression _applySuccessProgression(
    ExerciseProgression progression,
    List<ProgressionHistory> history,
    DateTime now,
    UserProfile userProfile,
  ) {
    final newSuccesses = progression.consecutiveSuccesses + 1;

    // Only progress after 2+ consecutive successes
    if (newSuccesses < 2) {
      return progression.copyWith(
        consecutiveSuccesses: newSuccesses,
        consecutiveFailures: 0,
        lastUpdated: now,
      );
    }

    int newReps = progression.currentReps;
    double newWeight = progression.currentWeight;
    int newDuration = progression.currentDuration;
    String description = '';

    switch (progression.type) {
      case ExerciseType.bodyweight:
        // Increase reps by 1-2
        newReps = progression.currentReps + (newSuccesses >= 3 ? 2 : 1);
        description = 'Progression: Increased reps from ${progression.currentReps} to $newReps';
        break;
      case ExerciseType.dumbbell:
        // Increase weight by 2-5% or add reps if no heavier weight available
        final weightIncrease = progression.currentWeight * 0.025; // 2.5%
        newWeight = progression.currentWeight + weightIncrease;
        description = 'Progression: Increased weight from ${progression.currentWeight.toStringAsFixed(1)}kg to ${newWeight.toStringAsFixed(1)}kg';
        break;
      case ExerciseType.timedHold:
        // Increase duration by 5-10 seconds
        newDuration = progression.currentDuration + (newSuccesses >= 3 ? 10 : 5);
        description = 'Progression: Increased duration from ${progression.currentDuration}s to ${newDuration}s';
        break;
      case ExerciseType.cardio:
        // Increase duration by 30-60 seconds
        newDuration = progression.currentDuration + (newSuccesses >= 3 ? 60 : 30);
        description = 'Progression: Increased duration from ${progression.currentDuration}s to ${newDuration}s';
        break;
    }

    history.add(ProgressionHistory(
      date: now,
      changeType: 'progression',
      description: description,
      oldReps: progression.currentReps,
      newReps: newReps,
      oldWeight: progression.currentWeight,
      newWeight: newWeight,
      oldDuration: progression.currentDuration,
      newDuration: newDuration,
    ));

    return progression.copyWith(
      currentReps: newReps,
      currentWeight: newWeight,
      currentDuration: newDuration,
      consecutiveSuccesses: 0, // Reset after progression
      consecutiveFailures: 0,
      lastUpdated: now,
      history: history,
    );
  }

  /// Apply regression for failed completion
  ExerciseProgression _applyFailureProgression(
    ExerciseProgression progression,
    List<ProgressionHistory> history,
    DateTime now,
  ) {
    final newFailures = progression.consecutiveFailures + 1;

    // Only regress after 2+ consecutive failures
    if (newFailures < 2) {
      return progression.copyWith(
        consecutiveSuccesses: 0,
        consecutiveFailures: newFailures,
        lastUpdated: now,
      );
    }

    int newReps = progression.currentReps;
    double newWeight = progression.currentWeight;
    int newDuration = progression.currentDuration;
    String description = '';

    switch (progression.type) {
      case ExerciseType.bodyweight:
        // Decrease reps by 1
        newReps = (progression.currentReps - 1).clamp(1, 100);
        description = 'Regression: Reduced reps from ${progression.currentReps} to $newReps';
        break;
      case ExerciseType.dumbbell:
        // Decrease weight by 2-5%
        newWeight = progression.currentWeight * 0.95;
        description = 'Regression: Reduced weight from ${progression.currentWeight.toStringAsFixed(1)}kg to ${newWeight.toStringAsFixed(1)}kg';
        break;
      case ExerciseType.timedHold:
        // Decrease duration by 5 seconds
        newDuration = (progression.currentDuration - 5).clamp(5, 3600);
        description = 'Regression: Reduced duration from ${progression.currentDuration}s to ${newDuration}s';
        break;
      case ExerciseType.cardio:
        // Decrease duration by 30 seconds
        newDuration = (progression.currentDuration - 30).clamp(30, 7200);
        description = 'Regression: Reduced duration from ${progression.currentDuration}s to ${newDuration}s';
        break;
    }

    history.add(ProgressionHistory(
      date: now,
      changeType: 'failure',
      description: description,
      oldReps: progression.currentReps,
      newReps: newReps,
      oldWeight: progression.currentWeight,
      newWeight: newWeight,
      oldDuration: progression.currentDuration,
      newDuration: newDuration,
    ));

    return progression.copyWith(
      currentReps: newReps,
      currentWeight: newWeight,
      currentDuration: newDuration,
      consecutiveSuccesses: 0,
      consecutiveFailures: 0, // Reset after regression
      lastUpdated: now,
      history: history,
    );
  }

  /// Determine exercise type based on exercise properties
  ExerciseType _determineExerciseType(Exercise exercise) {
    final name = exercise.name.toLowerCase();

    if (exercise.targetWeight != null && exercise.targetWeight! > 0) {
      return ExerciseType.dumbbell;
    } else if (exercise.duration != null &&
               (name.contains('plank') || name.contains('hold') || name.contains('wall sit'))) {
      return ExerciseType.timedHold;
    } else if (exercise.duration != null &&
               (name.contains('run') || name.contains('cycle') || name.contains('cardio'))) {
      return ExerciseType.cardio;
    } else {
      return ExerciseType.bodyweight;
    }
  }

  /// Calculate total calories burned for a workout
  Future<double> calculateWorkoutCalories({
    required List<Exercise> exercises,
    required Map<String, ExerciseProgression> progressions,
    required UserProfile userProfile,
    required int workoutDurationMinutes,
  }) async {
    double totalCalories = 0.0;
    final userWeightKg = userProfile.weightUnit == 'kg'
        ? userProfile.weight
        : userProfile.weight * 0.453592; // Convert lbs to kg

    for (final exercise in exercises) {
      if (!exercise.isCompleted) continue;

      final progression = progressions[exercise.id];
      if (progression == null) continue;

      // Estimate time spent on this exercise (rough calculation)
      final estimatedMinutes = (progression.currentSets * 0.5) +
                              (progression.currentSets * 0.5); // 30s per set + 30s rest

      final calories = progression.calculateCaloriesBurned(userWeightKg, estimatedMinutes.round());
      totalCalories += calories;
    }

    return totalCalories;
  }
}

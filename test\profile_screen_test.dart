import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:personal_training_app/screens/profile_screen.dart';

void main() {
  group('ProfileScreen Tests', () {
    testWidgets('shows loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ProfileScreen(),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays profile screen correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ProfileScreen(),
        ),
      );

      expect(find.byType(ProfileScreen), findsOneWidget);
      // Check for app bar title specifically, not bottom navigation
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('has bottom navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ProfileScreen(),
        ),
      );

      // Check for bottom navigation
      expect(find.byType(BottomNavigationBar), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Workout'), findsOneWidget);
      expect(find.text('Progress'), findsOneWidget);
    });
  });
}

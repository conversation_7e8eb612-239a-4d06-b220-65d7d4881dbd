// User profile model
class UserProfile {
  String name;
  int age;
  double height;
  double weight;
  String weighInDay;
  String weightUnit; // 'kg' or 'lbs'
  String heightUnit; // 'cm' or 'ft'

  UserProfile({
    required this.name,
    required this.age,
    required this.height,
    required this.weight,
    required this.weighInDay,
    this.weightUnit = 'kg',
    this.heightUnit = 'cm',
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'age': age,
      'height': height,
      'weight': weight,
      'weighInDay': weighInDay,
      'weightUnit': weightUnit,
      'heightUnit': heightUnit,
    };
  }

  // Create from JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      name: json['name'] ?? 'User',
      age: json['age'],
      height: json['height'].toDouble(),
      weight: json['weight'].toDouble(),
      weighInDay: json['weighInDay'],
      weightUnit: json['weightUnit'] ?? 'kg',
      heightUnit: json['heightUnit'] ?? 'cm',
    );
  }

  // Helper methods for display
  String get formattedWeight {
    return '${weight.toStringAsFixed(1)} $weightUnit';
  }

  String get formattedHeight {
    if (heightUnit == 'ft') {
      final feet = height.floor();
      final inches = ((height - feet) * 12).round();
      return '$feet\'$inches"';
    }
    return '${height.toStringAsFixed(0)} $heightUnit';
  }

  // Convert weight between units
  double get weightInKg {
    return weightUnit == 'lbs' ? weight * 0.453592 : weight;
  }

  double get weightInLbs {
    return weightUnit == 'kg' ? weight * 2.20462 : weight;
  }

  // Convert height between units
  double get heightInCm {
    return heightUnit == 'ft' ? height * 30.48 : height;
  }

  double get heightInFt {
    return heightUnit == 'cm' ? height / 30.48 : height;
  }

  // BMI calculation (always uses metric)
  double get bmi {
    final heightInMeters = heightInCm / 100;
    return weightInKg / (heightInMeters * heightInMeters);
  }
}

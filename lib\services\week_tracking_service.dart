import 'dart:convert';
import 'local_storage.dart';

/// Service for tracking workout weeks and deload cycles
class WeekTrackingService {
  static const String _weekDataKey = 'week_tracking_data';
  final LocalStorageService _storage = LocalStorageService();

  /// Load week tracking data
  Future<WeekTrackingData> loadWeekData() async {
    try {
      final data = await _storage.loadData(_weekDataKey);
      if (data == null) {
        // Initialize with week 1
        return WeekTrackingData(
          currentWeek: 1,
          startDate: DateTime.now(),
          lastWorkoutDate: null,
          totalWorkoutsCompleted: 0,
        );
      }

      final Map<String, dynamic> jsonMap = jsonDecode(data);
      return WeekTrackingData.fromJson(jsonMap);
    } catch (e) {
      print('Error loading week data: $e');
      return WeekTrackingData(
        currentWeek: 1,
        startDate: DateTime.now(),
        lastWorkoutDate: null,
        totalWorkoutsCompleted: 0,
      );
    }
  }

  /// Save week tracking data
  Future<void> saveWeekData(WeekTrackingData weekData) async {
    try {
      await _storage.saveData(_weekDataKey, jsonEncode(weekData.toJson()));
    } catch (e) {
      print('Error saving week data: $e');
    }
  }

  /// Update week data after workout completion
  Future<WeekTrackingData> updateAfterWorkout() async {
    final weekData = await loadWeekData();
    final now = DateTime.now();
    
    // Check if we need to advance to the next week
    final daysSinceStart = now.difference(weekData.startDate).inDays;
    final calculatedWeek = (daysSinceStart ~/ 7) + 1;
    
    final updatedWeekData = weekData.copyWith(
      currentWeek: calculatedWeek,
      lastWorkoutDate: now,
      totalWorkoutsCompleted: weekData.totalWorkoutsCompleted + 1,
    );
    
    await saveWeekData(updatedWeekData);
    return updatedWeekData;
  }

  /// Check if current week is a deload week (every 5th week)
  Future<bool> isDeloadWeek() async {
    final weekData = await loadWeekData();
    return weekData.currentWeek % 5 == 0;
  }

  /// Get current week number
  Future<int> getCurrentWeek() async {
    final weekData = await loadWeekData();
    return weekData.currentWeek;
  }

  /// Reset week tracking (for testing or user request)
  Future<void> resetWeekTracking() async {
    final newWeekData = WeekTrackingData(
      currentWeek: 1,
      startDate: DateTime.now(),
      lastWorkoutDate: null,
      totalWorkoutsCompleted: 0,
    );
    await saveWeekData(newWeekData);
  }

  /// Get weeks until next deload
  Future<int> getWeeksUntilDeload() async {
    final weekData = await loadWeekData();
    final weeksInCycle = weekData.currentWeek % 5;
    return weeksInCycle == 0 ? 5 : 5 - weeksInCycle;
  }
}

/// Data class for tracking workout weeks
class WeekTrackingData {
  final int currentWeek;
  final DateTime startDate;
  final DateTime? lastWorkoutDate;
  final int totalWorkoutsCompleted;

  WeekTrackingData({
    required this.currentWeek,
    required this.startDate,
    this.lastWorkoutDate,
    required this.totalWorkoutsCompleted,
  });

  Map<String, dynamic> toJson() {
    return {
      'currentWeek': currentWeek,
      'startDate': startDate.toIso8601String(),
      'lastWorkoutDate': lastWorkoutDate?.toIso8601String(),
      'totalWorkoutsCompleted': totalWorkoutsCompleted,
    };
  }

  factory WeekTrackingData.fromJson(Map<String, dynamic> json) {
    return WeekTrackingData(
      currentWeek: json['currentWeek'],
      startDate: DateTime.parse(json['startDate']),
      lastWorkoutDate: json['lastWorkoutDate'] != null 
          ? DateTime.parse(json['lastWorkoutDate'])
          : null,
      totalWorkoutsCompleted: json['totalWorkoutsCompleted'],
    );
  }

  WeekTrackingData copyWith({
    int? currentWeek,
    DateTime? startDate,
    DateTime? lastWorkoutDate,
    int? totalWorkoutsCompleted,
  }) {
    return WeekTrackingData(
      currentWeek: currentWeek ?? this.currentWeek,
      startDate: startDate ?? this.startDate,
      lastWorkoutDate: lastWorkoutDate ?? this.lastWorkoutDate,
      totalWorkoutsCompleted: totalWorkoutsCompleted ?? this.totalWorkoutsCompleted,
    );
  }

  /// Get formatted week display text
  String get weekDisplayText {
    if (currentWeek % 5 == 0) {
      return 'Week $currentWeek (Deload Week)';
    } else {
      return 'Week $currentWeek';
    }
  }

  /// Get phase based on current week
  String get currentPhase {
    if (currentWeek <= 4) {
      return 'Beginner';
    } else if (currentWeek <= 12) {
      return 'Intermediate';
    } else {
      return 'Advanced';
    }
  }

  /// Get workouts per week recommendation based on phase
  int get recommendedWorkoutsPerWeek {
    switch (currentPhase) {
      case 'Beginner':
        return 3;
      case 'Intermediate':
        return 4;
      case 'Advanced':
        return 5;
      default:
        return 3;
    }
  }

  /// Get workout duration recommendation based on phase
  String get recommendedDuration {
    switch (currentPhase) {
      case 'Beginner':
        return '20-30min';
      case 'Intermediate':
        return '30-45min';
      case 'Advanced':
        return '45-60min';
      default:
        return '20-30min';
    }
  }
}

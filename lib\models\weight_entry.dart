// Weight entry model
class WeightEntry {
  final String id;
  final double weight;
  final DateTime date;
  final String? notes;
  final String unit; // 'kg' or 'lbs'

  WeightEntry({
    required this.id,
    required this.weight,
    required this.date,
    this.notes,
    this.unit = 'kg',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'weight': weight,
      'date': date.toIso8601String(),
      'notes': notes,
      'unit': unit,
    };
  }

  factory WeightEntry.fromJson(Map<String, dynamic> json) {
    return WeightEntry(
      id: json['id'],
      weight: json['weight'].toDouble(),
      date: DateTime.parse(json['date']),
      notes: json['notes'],
      unit: json['unit'] ?? 'kg',
    );
  }

  WeightEntry copyWith({
    String? id,
    double? weight,
    DateTime? date,
    String? notes,
    String? unit,
  }) {
    return WeightEntry(
      id: id ?? this.id,
      weight: weight ?? this.weight,
      date: date ?? this.date,
      notes: notes ?? this.notes,
      unit: unit ?? this.unit,
    );
  }

  String get formattedDate {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String get shortFormattedDate {
    return '${date.day}/${date.month}';
  }

  String get formattedWeight {
    return '${weight.toStringAsFixed(1)} $unit';
  }

  // Calculate days since this entry
  int get daysSince {
    return DateTime.now().difference(date).inDays;
  }

  // Check if this entry is from today
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  // Check if this entry is from this week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1)));
  }
}

// Weight statistics helper class
class WeightStats {
  final double currentWeight;
  final double? previousWeight;
  final double? weeklyChange;
  final double? monthlyChange;
  final double? totalChange;
  final double? startingWeight;
  final int totalEntries;
  final int streakDays;

  WeightStats({
    required this.currentWeight,
    this.previousWeight,
    this.weeklyChange,
    this.monthlyChange,
    this.totalChange,
    this.startingWeight,
    required this.totalEntries,
    required this.streakDays,
  });

  String get weeklyChangeText {
    if (weeklyChange == null) return 'No change';
    final sign = weeklyChange! >= 0 ? '+' : '';
    return '$sign${weeklyChange!.toStringAsFixed(1)} kg';
  }

  String get weeklyChangeDescription {
    if (weeklyChange == null) return 'No previous data';
    if (weeklyChange! < 0) return 'Lost this week';
    if (weeklyChange! > 0) return 'Gained this week';
    return 'No change this week';
  }

  bool get isLosingWeight => weeklyChange != null && weeklyChange! < 0;
  bool get isGainingWeight => weeklyChange != null && weeklyChange! > 0;
  bool get isStableWeight => weeklyChange != null && weeklyChange!.abs() < 0.1;
}

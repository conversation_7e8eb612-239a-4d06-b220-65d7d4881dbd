import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:personal_training_app/main.dart';
import 'package:personal_training_app/models/user_profile.dart';
import 'package:personal_training_app/services/local_storage.dart';

void main() {
  group('Navigation Flow Tests', () {
    setUp(() async {
      // Clear SharedPreferences and set up test environment
      SharedPreferences.setMockInitialValues({});
      LocalStorageService.resetForTesting();
      await LocalStorageService.init();
    });

    testWidgets('should navigate correctly through bottom navigation without clearing stack', (WidgetTester tester) async {
      // Create a test profile to skip onboarding
      final testProfile = UserProfile(
        name: 'Test User',
        age: 25,
        height: 170.0,
        weight: 70.0,
        weighInDay: 'Monday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
      await LocalStorageService().saveUserProfile(testProfile);

      // Build the app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Should start at home screen (after splash)
      expect(find.text('Personal Trainer'), findsOneWidget);
      expect(find.text('Start Workout'), findsOneWidget);

      // Navigate to workout screen via bottom navigation
      final workoutNavButton = find.descendant(
        of: find.byType(BottomNavigationBar),
        matching: find.byIcon(Icons.fitness_center),
      );
      await tester.tap(workoutNavButton);
      await tester.pumpAndSettle();

      // Should be on workout screen
      expect(find.text('Workout'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // Test back button - should go back to home
      await tester.tap(find.byIcon(Icons.arrow_back_ios));
      await tester.pumpAndSettle();

      // Should be back at home screen
      expect(find.text('Start Workout'), findsOneWidget);

      // Navigate to profile via bottom navigation
      final profileNavButton = find.descendant(
        of: find.byType(BottomNavigationBar),
        matching: find.byIcon(Icons.person_outline),
      );
      await tester.tap(profileNavButton);
      await tester.pumpAndSettle();

      // Should be on profile screen
      expect(find.text('Profile'), findsOneWidget);
      expect(find.text('Test User'), findsOneWidget);

      // Navigate to history via bottom navigation
      final historyNavButton = find.descendant(
        of: find.byType(BottomNavigationBar),
        matching: find.byIcon(Icons.history),
      );
      await tester.tap(historyNavButton);
      await tester.pumpAndSettle();

      // Should be on history screen
      expect(find.text('History'), findsOneWidget);

      // Navigate back to home via bottom navigation
      final homeNavButton = find.descendant(
        of: find.byType(BottomNavigationBar),
        matching: find.byIcon(Icons.home_outlined),
      );
      await tester.tap(homeNavButton);
      await tester.pumpAndSettle();

      // Should be back at home screen
      expect(find.text('Start Workout'), findsOneWidget);
    });

    testWidgets('should handle profile edit navigation correctly', (WidgetTester tester) async {
      // Create a test profile
      final testProfile = UserProfile(
        name: 'Edit Test User',
        age: 30,
        height: 175.0,
        weight: 80.0,
        weighInDay: 'Wednesday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
      await LocalStorageService().saveUserProfile(testProfile);

      // Build the app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to profile screen via app bar
      final profileAppBarButton = find.descendant(
        of: find.byType(AppBar),
        matching: find.byIcon(Icons.person),
      );
      await tester.tap(profileAppBarButton);
      await tester.pumpAndSettle();

      // Should be on profile screen
      expect(find.text('Edit Test User'), findsOneWidget);

      // Tap edit profile button
      await tester.tap(find.text('Edit Profile'));
      await tester.pumpAndSettle();

      // Should be on profile edit screen
      expect(find.text('Edit Profile'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);

      // Test close button - should go back to profile screen
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Should be back at profile screen
      expect(find.text('Edit Test User'), findsOneWidget);
      expect(find.text('Edit Profile'), findsOneWidget);
    });

    testWidgets('should handle workout completion navigation correctly', (WidgetTester tester) async {
      // Create a test profile
      final testProfile = UserProfile(
        name: 'Workout Test User',
        age: 28,
        height: 180.0,
        weight: 75.0,
        weighInDay: 'Friday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
      await LocalStorageService().saveUserProfile(testProfile);

      // Build the app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to workout screen
      await tester.tap(find.text('Start Workout'));
      await tester.pumpAndSettle();

      // Should be on workout screen
      expect(find.text('Workout'), findsOneWidget);

      // Find and tap the finish workout button
      final finishButton = find.text('Finish Workout');
      if (finishButton.evaluate().isNotEmpty) {
        await tester.tap(finishButton);
        await tester.pumpAndSettle();

        // After finishing workout, should be able to go back
        // The workout screen should still be accessible via back button
        expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
      }
    });

    testWidgets('should preserve navigation stack when using bottom navigation', (WidgetTester tester) async {
      // Create a test profile
      final testProfile = UserProfile(
        name: 'Stack Test User',
        age: 35,
        height: 165.0,
        weight: 65.0,
        weighInDay: 'Sunday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
      await LocalStorageService().saveUserProfile(testProfile);

      // Build the app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Start at home, navigate to profile via app bar
      final profileAppBarButton = find.descendant(
        of: find.byType(AppBar),
        matching: find.byIcon(Icons.person),
      );
      await tester.tap(profileAppBarButton);
      await tester.pumpAndSettle();

      // Should be on profile screen
      expect(find.text('Stack Test User'), findsOneWidget);

      // Navigate to workout via bottom navigation
      final workoutNavButton = find.descendant(
        of: find.byType(BottomNavigationBar),
        matching: find.byIcon(Icons.fitness_center),
      );
      await tester.tap(workoutNavButton);
      await tester.pumpAndSettle();

      // Should be on workout screen
      expect(find.text('Workout'), findsOneWidget);

      // Use back button - should go back to profile (not splash screen)
      await tester.tap(find.byIcon(Icons.arrow_back_ios));
      await tester.pumpAndSettle();

      // Should be back at profile screen, not splash screen
      expect(find.text('Stack Test User'), findsOneWidget);
      expect(find.text('Personal Trainer'), findsNothing); // Not splash screen
    });

    testWidgets('should handle deep navigation stack correctly', (WidgetTester tester) async {
      // Create a test profile
      final testProfile = UserProfile(
        name: 'Deep Nav User',
        age: 40,
        height: 170.0,
        weight: 70.0,
        weighInDay: 'Tuesday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
      await LocalStorageService().saveUserProfile(testProfile);

      // Build the app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Home -> Profile -> Edit Profile -> Back -> Back -> Should be at Home
      
      // Navigate to profile via app bar
      final profileAppBarButton = find.descendant(
        of: find.byType(AppBar),
        matching: find.byIcon(Icons.person),
      );
      await tester.tap(profileAppBarButton);
      await tester.pumpAndSettle();
      expect(find.text('Deep Nav User'), findsOneWidget);

      // Navigate to edit profile
      await tester.tap(find.text('Edit Profile'));
      await tester.pumpAndSettle();
      expect(find.text('Edit Profile'), findsOneWidget);

      // Go back to profile
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();
      expect(find.text('Deep Nav User'), findsOneWidget);

      // Go back to home (using system back button simulation)
      // Since we can't easily simulate system back button in widget tests,
      // we'll verify the navigation structure is preserved by checking
      // that we're still on the profile screen and not splash screen
      expect(find.text('Deep Nav User'), findsOneWidget);
      expect(find.text('Personal Trainer'), findsNothing); // Not splash screen
    });
  });
}

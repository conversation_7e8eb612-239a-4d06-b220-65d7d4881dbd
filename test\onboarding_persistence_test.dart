import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:personal_training_app/models/user_profile.dart';
import 'package:personal_training_app/services/local_storage.dart';

void main() {
  group('Onboarding Persistence Tests', () {
    late LocalStorageService storageService;

    setUp(() {
      storageService = LocalStorageService();
    });

    testWidgets('should save and load user profile with units', (WidgetTester tester) async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      // Create a test profile with units
      final testProfile = UserProfile(
        name: 'Test User',
        age: 25,
        height: 175.0,
        weight: 70.0,
        weighInDay: 'Monday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );

      // Save the profile
      await storageService.saveUserProfile(testProfile);

      // Load the profile
      final loadedProfile = await storageService.loadUserProfile();

      // Verify the profile was saved and loaded correctly
      expect(loadedProfile, isNotNull);
      expect(loadedProfile!.name, equals('Test User'));
      expect(loadedProfile.age, equals(25));
      expect(loadedProfile.height, equals(175.0));
      expect(loadedProfile.weight, equals(70.0));
      expect(loadedProfile.weighInDay, equals('Monday'));
      expect(loadedProfile.weightUnit, equals('kg'));
      expect(loadedProfile.heightUnit, equals('cm'));
    });

    testWidgets('should handle imperial units correctly', (WidgetTester tester) async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      // Create a test profile with imperial units
      final testProfile = UserProfile(
        name: 'Imperial User',
        age: 30,
        height: 6.0, // 6 feet
        weight: 180.0, // 180 lbs
        weighInDay: 'Sunday',
        weightUnit: 'lbs',
        heightUnit: 'ft',
      );

      // Save the profile
      await storageService.saveUserProfile(testProfile);

      // Load the profile
      final loadedProfile = await storageService.loadUserProfile();

      // Verify the profile was saved and loaded correctly
      expect(loadedProfile, isNotNull);
      expect(loadedProfile!.weightUnit, equals('lbs'));
      expect(loadedProfile.heightUnit, equals('ft'));
      expect(loadedProfile.formattedWeight, equals('180.0 lbs'));
      expect(loadedProfile.formattedHeight, equals('6\'0"'));
    });

    testWidgets('should calculate BMI correctly regardless of units', (WidgetTester tester) async {
      // Test metric profile
      final metricProfile = UserProfile(
        name: 'Metric User',
        age: 25,
        height: 175.0, // cm
        weight: 70.0, // kg
        weighInDay: 'Monday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );

      // Test imperial profile (same person)
      final imperialProfile = UserProfile(
        name: 'Imperial User',
        age: 25,
        height: 5.74, // feet (approximately 175cm)
        weight: 154.3, // lbs (approximately 70kg)
        weighInDay: 'Monday',
        weightUnit: 'lbs',
        heightUnit: 'ft',
      );

      // BMI should be approximately the same
      final metricBMI = metricProfile.bmi;
      final imperialBMI = imperialProfile.bmi;

      expect((metricBMI - imperialBMI).abs(), lessThan(0.1)); // Within 0.1 BMI points
    });

    testWidgets('should default to metric units when not specified', (WidgetTester tester) async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      // Create a profile without specifying units (should default to metric)
      final testProfile = UserProfile(
        name: 'Default User',
        age: 25,
        height: 175.0,
        weight: 70.0,
        weighInDay: 'Monday',
      );

      expect(testProfile.weightUnit, equals('kg'));
      expect(testProfile.heightUnit, equals('cm'));
    });
  });
}

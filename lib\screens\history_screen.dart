// HistoryScreen: Shows workout and weight history
import 'package:flutter/material.dart';
import '../widgets/bottom_navigation.dart';
import '../widgets/workout_history_tile.dart';
import '../widgets/weight_entry_tile.dart';
import '../models/workout_history.dart';
import '../models/weight_entry.dart';
import '../services/workout_service.dart';
import '../services/weight_service.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WorkoutService _workoutService = WorkoutService();
  final WeightService _weightService = WeightService();
  List<WorkoutHistory> _workoutHistory = [];
  List<WeightEntry> _weightEntries = [];
  WeightStats? _weightStats;
  bool _isLoading = true;
  bool _isAddingEntry = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadWorkoutHistory(),
      _loadWeightData(),
    ]);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadWorkoutHistory() async {
    try {
      final history = await _workoutService.getWorkoutHistory();
      setState(() {
        _workoutHistory = history;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadWeightData() async {
    try {
      final entries = await _weightService.getWeightEntries();
      final stats = await _weightService.getWeightStats();

      setState(() {
        _weightEntries = entries;
        _weightStats = stats;
      });
    } catch (e) {
      // Handle error silently for now
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Progress',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xFF00ff00),
          labelColor: const Color(0xFF00ff00),
          unselectedLabelColor: const Color(0xFFa3a3a3),
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(text: 'Workout Log'),
            Tab(text: 'Weight Log'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildWorkoutLogTab(),
          _buildWeightLogTab(),
        ],
      ),
      bottomNavigationBar: const CustomBottomNavigation(currentIndex: 2),
    );
  }

  Widget _buildWorkoutLogTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF00ff00)),
      );
    }

    if (_workoutHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.fitness_center,
              size: 80,
              color: Color(0xFFa3a3a3),
            ),
            const SizedBox(height: 24),
            const Text(
              'No workouts yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Complete your first workout to see it here',
              style: TextStyle(
                color: Color(0xFFa3a3a3),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Workout History',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _workoutHistory.length,
            itemBuilder: (context, index) {
              final workout = _workoutHistory[index];
              return WorkoutHistoryTile(
                workoutHistory: workout,
                onTap: () {
                  // TODO: Navigate to workout details
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Workout details for ${workout.workoutName}'),
                      backgroundColor: const Color(0xFF00ff00),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildWeightLogTab() {
    return Column(
      children: [
        // Weight stats header
        if (_weightStats != null) _buildStatsHeader(),

        // Weight chart
        Padding(
          padding: const EdgeInsets.all(16),
          child: WeightChart(
            entries: _weightEntries,
            height: 160,
          ),
        ),

        // Add weight button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddWeightDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Weight Entry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00ff00),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Weight entries list
        Expanded(
          child: _weightEntries.isEmpty
              ? _buildEmptyWeightState()
              : _buildWeightEntriesList(),
        ),
      ],
    );
  }

  Widget _buildStatsHeader() {
    final stats = _weightStats!;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1a1a1a),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'Current Weight',
                '${stats.currentWeight.toStringAsFixed(1)} kg',
                const Color(0xFF00ff00),
              ),
              _buildStatItem(
                'Weekly Change',
                stats.weeklyChangeText,
                stats.isLosingWeight
                    ? const Color(0xFF00ff00)
                    : stats.isGainingWeight
                        ? Colors.red
                        : const Color(0xFFa3a3a3),
              ),
              _buildStatItem(
                'Streak',
                '${stats.streakDays} days',
                const Color(0xFF00ff00),
              ),
            ],
          ),
          if (stats.totalChange != null) ...[
            const SizedBox(height: 16),
            Text(
              'Total change: ${stats.totalChange! >= 0 ? '+' : ''}${stats.totalChange!.toStringAsFixed(1)} kg',
              style: TextStyle(
                color: stats.totalChange! < 0
                    ? const Color(0xFF00ff00)
                    : Colors.red,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFFa3a3a3),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyWeightState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.monitor_weight,
            size: 80,
            color: Color(0xFFa3a3a3),
          ),
          const SizedBox(height: 24),
          const Text(
            'No weight entries yet',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Start tracking your weight progress',
            style: TextStyle(
              color: Color(0xFFa3a3a3),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightEntriesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Weight History',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _weightEntries.length,
            itemBuilder: (context, index) {
              final entry = _weightEntries[index];
              final previousEntry = index < _weightEntries.length - 1
                  ? _weightEntries[index + 1]
                  : null;

              return WeightEntryTile(
                weightEntry: entry,
                showChange: previousEntry != null,
                previousWeight: previousEntry?.weight,
                onTap: () => _showEditWeightDialog(entry),
                onDelete: () => _deleteWeightEntry(entry),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _showAddWeightDialog() async {
    final TextEditingController weightController = TextEditingController();
    final TextEditingController notesController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    final userWeightUnit = await _weightService.getUserWeightUnit();

    if (!mounted) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          return AlertDialog(
            backgroundColor: const Color(0xFF1a1a1a),
            title: const Text(
              'Add Weight Entry',
              style: TextStyle(color: Colors.white),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Date selector
                ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: const Icon(Icons.calendar_today, color: Color(0xFF00ff00)),
                  title: Text(
                    'Date: ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                    style: const TextStyle(color: Colors.white),
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: const ColorScheme.dark(
                              primary: Color(0xFF00ff00),
                              surface: Color(0xFF1a1a1a),
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (date != null) {
                      setDialogState(() {
                        selectedDate = date;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                // Weight input
                TextField(
                  controller: weightController,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: 'Weight ($userWeightUnit)',
                    labelStyle: TextStyle(color: Color(0xFFa3a3a3)),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF1a1a1a)),
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF00ff00)),
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    filled: true,
                    fillColor: Color(0xFF2a2a2a),
                  ),
                ),
                const SizedBox(height: 16),
                // Notes input
                TextField(
                  controller: notesController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    labelText: 'Notes (optional)',
                    labelStyle: TextStyle(color: Color(0xFFa3a3a3)),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF1a1a1a)),
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF00ff00)),
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    filled: true,
                    fillColor: Color(0xFF2a2a2a),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Color(0xFFa3a3a3)),
                ),
              ),
              ElevatedButton(
                onPressed: _isAddingEntry ? null : () => _saveWeightEntry(context, weightController, notesController, selectedDate),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF00ff00),
                  foregroundColor: Colors.black,
                ),
                child: _isAddingEntry
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.black,
                        ),
                      )
                    : const Text('Save'),
              ),
            ],
          );
        },
      ),
    );

    if (result == true) {
      await _loadWeightData();
    }
  }

  Future<void> _saveWeightEntry(BuildContext dialogContext, TextEditingController weightController, TextEditingController notesController, DateTime selectedDate) async {
    final weightText = weightController.text.trim();
    if (weightText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a weight')),
      );
      return;
    }

    final weight = double.tryParse(weightText);
    if (weight == null || weight <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid weight')),
      );
      return;
    }

    setState(() {
      _isAddingEntry = true;
    });

    // Store context before async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(dialogContext);

    try {
      final userWeightUnit = await _weightService.getUserWeightUnit();
      final entry = WeightEntry(
        id: 'entry_${DateTime.now().millisecondsSinceEpoch}',
        weight: weight,
        date: selectedDate,
        notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
        unit: userWeightUnit,
      );

      await _weightService.addWeightEntry(entry);

      if (mounted) {
        navigator.pop(true);
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Weight entry added successfully!'),
            backgroundColor: Color(0xFF00ff00),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(content: Text('Failed to save weight entry')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAddingEntry = false;
        });
      }
    }
  }

  Future<void> _showEditWeightDialog(WeightEntry entry) async {
    // TODO: Implement edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit functionality coming soon!'),
        backgroundColor: Color(0xFF00ff00),
      ),
    );
  }

  Future<void> _deleteWeightEntry(WeightEntry entry) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1a1a1a),
        title: const Text(
          'Delete Weight Entry',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to delete the weight entry for ${entry.formattedDate}?',
          style: const TextStyle(color: Color(0xFFa3a3a3)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Color(0xFFa3a3a3)),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _weightService.deleteWeightEntry(entry.id);
        await _loadWeightData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Weight entry deleted'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to delete weight entry')),
          );
        }
      }
    }
  }
}

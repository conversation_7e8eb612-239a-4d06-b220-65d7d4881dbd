import 'dart:convert';
import '../models/weight_entry.dart';
import '../models/user_profile.dart';
import 'local_storage.dart';

class WeightService {
  final LocalStorageService _storage = LocalStorageService();
  static const String _weightEntriesKey = 'weight_entries';

  // Add a new weight entry
  Future<void> addWeightEntry(WeightEntry entry) async {
    final entries = await getWeightEntries();

    // Remove any existing entry for the same date
    entries.removeWhere((e) =>
      e.date.year == entry.date.year &&
      e.date.month == entry.date.month &&
      e.date.day == entry.date.day
    );

    // Add new entry and sort by date (newest first)
    entries.add(entry);
    entries.sort((a, b) => b.date.compareTo(a.date));

    await _saveWeightEntries(entries);
  }

  // Get user's preferred weight unit
  Future<String> getUserWeightUnit() async {
    final profile = await _storage.loadUserProfile();
    return profile?.weightUnit ?? 'kg';
  }

  // Get all weight entries
  Future<List<WeightEntry>> getWeightEntries() async {
    final data = await _storage.loadData(_weightEntriesKey);
    if (data != null) {
      final List<dynamic> entriesJson = jsonDecode(data);
      final entries = entriesJson.map((e) => WeightEntry.fromJson(e)).toList();
      entries.sort((a, b) => b.date.compareTo(a.date)); // Sort newest first
      return entries;
    }
    return _getSampleEntries(); // Return sample data for demo
  }

  // Delete a weight entry
  Future<void> deleteWeightEntry(String entryId) async {
    final entries = await getWeightEntries();
    entries.removeWhere((e) => e.id == entryId);
    await _saveWeightEntries(entries);
  }

  // Update a weight entry
  Future<void> updateWeightEntry(WeightEntry updatedEntry) async {
    final entries = await getWeightEntries();
    final index = entries.indexWhere((e) => e.id == updatedEntry.id);
    if (index != -1) {
      entries[index] = updatedEntry;
      entries.sort((a, b) => b.date.compareTo(a.date));
      await _saveWeightEntries(entries);
    }
  }

  // Get weight statistics
  Future<WeightStats> getWeightStats() async {
    final entries = await getWeightEntries();
    if (entries.isEmpty) {
      return WeightStats(
        currentWeight: 0,
        totalEntries: 0,
        streakDays: 0,
      );
    }

    final currentEntry = entries.first;
    final currentWeight = currentEntry.weight;
    
    // Find previous week's entry
    final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
    WeightEntry? previousWeekEntry;
    for (final entry in entries) {
      if (entry.date.isBefore(oneWeekAgo)) {
        previousWeekEntry = entry;
        break;
      }
    }

    // Find starting weight (oldest entry)
    final startingWeight = entries.isNotEmpty ? entries.last.weight : null;

    // Calculate changes
    final weeklyChange = previousWeekEntry != null 
        ? currentWeight - previousWeekEntry.weight 
        : null;
    
    final totalChange = startingWeight != null 
        ? currentWeight - startingWeight 
        : null;

    // Calculate streak (consecutive days with entries)
    int streakDays = 0;
    DateTime checkDate = DateTime.now();
    
    for (int i = 0; i < 30; i++) { // Check last 30 days
      final hasEntry = entries.any((entry) =>
        entry.date.year == checkDate.year &&
        entry.date.month == checkDate.month &&
        entry.date.day == checkDate.day
      );
      
      if (hasEntry) {
        streakDays++;
      } else if (streakDays > 0) {
        break; // Streak broken
      }
      
      checkDate = checkDate.subtract(const Duration(days: 1));
    }

    return WeightStats(
      currentWeight: currentWeight,
      previousWeight: previousWeekEntry?.weight,
      weeklyChange: weeklyChange,
      totalChange: totalChange,
      startingWeight: startingWeight,
      totalEntries: entries.length,
      streakDays: streakDays,
    );
  }

  // Get current weight (most recent entry)
  Future<double?> getCurrentWeight() async {
    final entries = await getWeightEntries();
    return entries.isNotEmpty ? entries.first.weight : null;
  }

  // Check if user has logged weight today
  Future<bool> hasLoggedToday() async {
    final entries = await getWeightEntries();
    final today = DateTime.now();
    
    return entries.any((entry) =>
      entry.date.year == today.year &&
      entry.date.month == today.month &&
      entry.date.day == today.day
    );
  }

  // Get weight entries for chart (last N entries)
  Future<List<WeightEntry>> getWeightEntriesForChart({int limit = 30}) async {
    final entries = await getWeightEntries();
    final chartEntries = entries.take(limit).toList();
    chartEntries.sort((a, b) => a.date.compareTo(b.date)); // Sort oldest first for chart
    return chartEntries;
  }

  // Get weight trend (losing, gaining, stable)
  Future<String> getWeightTrend() async {
    final stats = await getWeightStats();
    if (stats.weeklyChange == null) return 'No data';
    
    if (stats.isLosingWeight) return 'Losing';
    if (stats.isGainingWeight) return 'Gaining';
    return 'Stable';
  }

  // Private helper methods
  Future<void> _saveWeightEntries(List<WeightEntry> entries) async {
    final entriesJson = entries.map((e) => e.toJson()).toList();
    await _storage.saveData(_weightEntriesKey, jsonEncode(entriesJson));
  }

  // Sample data for demonstration
  List<WeightEntry> _getSampleEntries() {
    final now = DateTime.now();
    return [
      WeightEntry(
        id: 'sample_1',
        weight: 75.2,
        date: now,
        unit: 'kg',
      ),
      WeightEntry(
        id: 'sample_2',
        weight: 75.8,
        date: now.subtract(const Duration(days: 7)),
        unit: 'kg',
      ),
      WeightEntry(
        id: 'sample_3',
        weight: 76.1,
        date: now.subtract(const Duration(days: 14)),
        unit: 'kg',
      ),
      WeightEntry(
        id: 'sample_4',
        weight: 76.5,
        date: now.subtract(const Duration(days: 21)),
        unit: 'kg',
      ),
      WeightEntry(
        id: 'sample_5',
        weight: 77.0,
        date: now.subtract(const Duration(days: 28)),
        unit: 'kg',
        notes: 'Starting weight',
      ),
    ];
  }

  // Initialize weight tracking with user's onboarding weight
  Future<void> initializeWithUserProfile(UserProfile profile) async {
    final entries = await getWeightEntries();
    
    // Only add initial entry if no entries exist
    if (entries.isEmpty || entries.every((e) => e.id.startsWith('sample_'))) {
      final initialEntry = WeightEntry(
        id: 'initial_${DateTime.now().millisecondsSinceEpoch}',
        weight: profile.weight,
        date: DateTime.now(),
        unit: profile.weightUnit,
        notes: 'Starting weight from onboarding',
      );
      
      await addWeightEntry(initialEntry);
    }
  }
}

import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import 'dart:convert';

class LocalStorageService {
  static const String userProfileKey = 'user_profile';
  static SharedPreferences? _prefs;

  // Initialize SharedPreferences once
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Reset singleton for testing
  static void resetForTesting() {
    _prefs = null;
  }

  Future<void> saveUserProfile(UserProfile profile) async {
    await init();
    final data = jsonEncode(profile.toJson());
    await _prefs!.setString(userProfileKey, data);
  }

  Future<UserProfile?> loadUserProfile() async {
    await init();
    final data = _prefs!.getString(userProfileKey);
    if (data == null) return null;

    try {
      final map = jsonDecode(data);
      return UserProfile.fromJson(map);
    } catch (e) {
      // If there's an error parsing, return null
      return null;
    }
  }

  // Generic methods for saving/loading data
  Future<void> saveData(String key, String data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, data);
  }

  Future<String?> loadData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  Future<void> removeData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
}

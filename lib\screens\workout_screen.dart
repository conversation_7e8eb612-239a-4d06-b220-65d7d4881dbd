// WorkoutScreen: Guides user through workout session
import 'package:flutter/material.dart';
import '../widgets/bottom_navigation.dart';
import '../widgets/exercise_tile.dart';
import '../widgets/progress_bar.dart';
import '../models/workout.dart';
import '../models/user_profile.dart';
import '../services/workout_service.dart';
import '../services/progression_service.dart';
import '../services/local_storage.dart';
import '../services/week_tracking_service.dart';

class WorkoutScreen extends StatefulWidget {
  const WorkoutScreen({super.key});

  @override
  State<WorkoutScreen> createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends State<WorkoutScreen> {
  final WorkoutService _workoutService = WorkoutService();
  final ProgressionService _progressionService = ProgressionService();
  final LocalStorageService _storageService = LocalStorageService();
  final WeekTrackingService _weekService = WeekTrackingService();
  Workout? _currentWorkout;
  UserProfile? _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWorkout();
  }

  Future<void> _loadWorkout() async {
    try {
      // Try to load current workout, or start a new one
      Workout? workout = await _workoutService.loadCurrentWorkout();
      workout ??= await _workoutService.startWorkout();

      // Load user profile for progression calculations
      final profile = await _storageService.loadUserProfile();

      setState(() {
        _currentWorkout = workout;
        _userProfile = profile;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleExercise(String exerciseId, bool isCompleted) async {
    if (_currentWorkout == null) return;

    try {
      final updatedWorkout = await _workoutService.updateExerciseCompletion(
        _currentWorkout!,
        exerciseId,
        isCompleted,
      );

      setState(() {
        _currentWorkout = updatedWorkout;
      });
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update exercise')),
        );
      }
    }
  }

  Future<void> _finishWorkout() async {
    if (_currentWorkout == null || _userProfile == null) return;

    try {
      // Apply progression logic for each exercise
      final progressions = await _progressionService.loadProgressions();
      final updatedProgressions = <String, dynamic>{};

      for (final exercise in _currentWorkout!.exercises) {
        // Get or create progression for this exercise
        var progression = progressions[exercise.id];
        progression ??= _progressionService.initializeProgression(exercise);

        // Apply progression based on completion status
        final updatedProgression = await _progressionService.applyProgression(
          progression: progression,
          allSetsCompleted: exercise.isCompleted,
          userProfile: _userProfile!,
        );

        updatedProgressions[exercise.id] = updatedProgression;
      }

      // Save updated progressions
      await _progressionService.saveProgressions(
        Map<String, dynamic>.from(updatedProgressions).cast(),
      );

      // Calculate calories burned with progression data
      final caloriesBurned = await _progressionService.calculateWorkoutCalories(
        exercises: _currentWorkout!.exercises,
        progressions: Map<String, dynamic>.from(updatedProgressions).cast(),
        userProfile: _userProfile!,
        workoutDurationMinutes: 30, // Estimate - could be tracked more precisely
      );

      // Update week tracking
      final weekData = await _weekService.updateAfterWorkout();

      // Finish the workout
      await _workoutService.finishWorkout(_currentWorkout!);

      if (mounted) {
        final weekText = weekData.currentWeek % 5 == 0 ? ' (Deload week completed!)' : '';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Workout completed! Burned ${caloriesBurned.round()} calories$weekText Great job!'),
            backgroundColor: const Color(0xFF00ff00),
            duration: const Duration(seconds: 4),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to finish workout')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _currentWorkout?.name ?? 'Workout',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_horiz, color: Colors.white),
            onPressed: () {
              // Show workout options
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF00ff00)),
            )
          : _currentWorkout == null
              ? _buildErrorView()
              : _buildWorkoutView(),
      bottomNavigationBar: const CustomBottomNavigation(currentIndex: 1),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Color(0xFFa3a3a3),
          ),
          const SizedBox(height: 24),
          const Text(
            'Failed to load workout',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadWorkout,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF00ff00),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutView() {
    final workout = _currentWorkout!;
    final exercisesByCategory = workout.exercisesByCategory;

    return Column(
      children: [
        // Workout header with calories
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Estimated Calories',
                style: const TextStyle(
                  color: Color(0xFFa3a3a3),
                  fontSize: 14,
                ),
              ),
              Text(
                '${workout.estimatedCalories} kcal',
                style: const TextStyle(
                  color: Color(0xFF00ff00),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        // Exercise list
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            children: [
              ...exercisesByCategory.entries.map((entry) {
                final category = entry.key;
                final exercises = entry.value;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        category,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    ...exercises.map((exercise) => ExerciseTile(
                      exercise: exercise,
                      onToggle: (isCompleted) => _toggleExercise(exercise.id, isCompleted),
                    )),
                  ],
                );
              }),
            ],
          ),
        ),
        // Footer with progress and finish button
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFF1a1a1a),
            border: Border(
              top: BorderSide(color: Color(0xFF2a2a2a), width: 1),
            ),
          ),
          child: Column(
            children: [
              ProgressBar(
                value: workout.completionPercentage,
                label: 'Workout Progress',
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: workout.completedExercises > 0 ? _finishWorkout : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF00ff00),
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    workout.isFullyCompleted
                        ? 'Finish Workout'
                        : 'Finish Workout (${workout.completedExercises}/${workout.totalExercises})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

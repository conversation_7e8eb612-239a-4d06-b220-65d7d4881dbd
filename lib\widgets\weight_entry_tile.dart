import 'package:flutter/material.dart';
import '../models/weight_entry.dart';

class WeightEntryTile extends StatelessWidget {
  const WeightEntryTile({
    super.key,
    required this.weightEntry,
    this.onTap,
    this.onDelete,
    this.showChange = false,
    this.previousWeight,
  });

  final WeightEntry weightEntry;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final bool showChange;
  final double? previousWeight;

  @override
  Widget build(BuildContext context) {
    final weightChange = previousWeight != null 
        ? weightEntry.weight - previousWeight! 
        : null;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1a1a1a),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Date and weight info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        weightEntry.formattedDate,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        weightEntry.formattedWeight,
                        style: const TextStyle(
                          color: Color(0xFF00ff00),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (weightEntry.notes != null && weightEntry.notes!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          weightEntry.notes!,
                          style: const TextStyle(
                            color: Color(0xFFa3a3a3),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Weight change indicator
                if (showChange && weightChange != null) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            weightChange < 0 
                                ? Icons.trending_down 
                                : weightChange > 0 
                                    ? Icons.trending_up 
                                    : Icons.trending_flat,
                            color: weightChange < 0 
                                ? const Color(0xFF00ff00) 
                                : weightChange > 0 
                                    ? Colors.red 
                                    : const Color(0xFFa3a3a3),
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${weightChange >= 0 ? '+' : ''}${weightChange.toStringAsFixed(1)} kg',
                            style: TextStyle(
                              color: weightChange < 0 
                                  ? const Color(0xFF00ff00) 
                                  : weightChange > 0 
                                      ? Colors.red 
                                      : const Color(0xFFa3a3a3),
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        weightChange < 0 ? 'Lost' : weightChange > 0 ? 'Gained' : 'Same',
                        style: const TextStyle(
                          color: Color(0xFFa3a3a3),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                ],
                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (onDelete != null)
                      IconButton(
                        onPressed: onDelete,
                        icon: const Icon(
                          Icons.delete_outline,
                          color: Colors.red,
                          size: 20,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    const Icon(
                      Icons.chevron_right,
                      color: Colors.white,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Simple weight chart widget (placeholder for now)
class WeightChart extends StatelessWidget {
  const WeightChart({
    super.key,
    required this.entries,
    this.height = 200,
  });

  final List<WeightEntry> entries;
  final double height;

  @override
  Widget build(BuildContext context) {
    if (entries.isEmpty) {
      return Container(
        height: height,
        constraints: BoxConstraints(
          minHeight: 120,
          maxHeight: height,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFF1a1a1a),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.show_chart,
                  color: Color(0xFFa3a3a3),
                  size: 40,
                ),
                const SizedBox(height: 12),
                const Text(
                  'No weight data to display',
                  style: TextStyle(
                    color: Color(0xFFa3a3a3),
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 6),
                const Text(
                  'Add weight entries to see your progress',
                  style: TextStyle(
                    color: Color(0xFFa3a3a3),
                    fontSize: 11,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Simple placeholder chart - in a real app, you'd use a charting library
    return Container(
      height: height,
      constraints: BoxConstraints(
        minHeight: 120,
        maxHeight: height,
      ),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1a1a1a),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Weight Progress',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.show_chart,
                      color: Color(0xFF00ff00),
                      size: 40,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '${entries.length} weight ${entries.length == 1 ? 'entry' : 'entries'}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (entries.length >= 2) ...[
                      const SizedBox(height: 6),
                      _buildWeightRange(entries),
                    ],
                    const SizedBox(height: 12),
                    const Text(
                      'Chart visualization coming soon!',
                      style: TextStyle(
                        color: Color(0xFFa3a3a3),
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightRange(List<WeightEntry> entries) {
    try {
      if (entries.isEmpty) return const SizedBox.shrink();

      final weights = entries.map((e) => e.weight).toList();
      if (weights.isEmpty) return const SizedBox.shrink();

      final minWeight = weights.reduce((a, b) => a < b ? a : b);
      final maxWeight = weights.reduce((a, b) => a > b ? a : b);

      return Text(
        'Range: ${minWeight.toStringAsFixed(1)} - ${maxWeight.toStringAsFixed(1)} kg',
        style: const TextStyle(
          color: Color(0xFFa3a3a3),
          fontSize: 12,
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }
}


import 'package:flutter/material.dart';
import '../services/dashboard_service.dart';
import '../services/week_tracking_service.dart';
import '../widgets/stat_card.dart';
import '../widgets/workout_card.dart';
import '../widgets/summary_stat_card.dart';
import '../widgets/bottom_navigation.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DashboardService _dashboardService = DashboardService();
  final WeekTrackingService _weekService = WeekTrackingService();
  DashboardStats? _stats;
  WeekTrackingData? _weekData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final stats = await _dashboardService.getDashboardStats();
      final weekData = await _weekService.loadWeekData();
      setState(() {
        _stats = stats;
        _weekData = weekData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _startWorkout() {
    Navigator.pushNamed(context, '/workout');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        automaticallyImplyLeading: false, // Removes the back button
        title: const Text(
          'Personal Trainer',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.person, color: Colors.white),
            onPressed: () {
              Navigator.pushNamed(context, '/profile');
            },
            tooltip: 'Profile',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF00ff00),
              ),
            )
          : _buildDashboard(),
      bottomNavigationBar: const CustomBottomNavigation(currentIndex: 0),
    );
  }

  Widget _buildDashboard() {
    if (_stats == null) {
      return const Center(
        child: Text(
          'Unable to load dashboard data',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Section - Weight and Streak
          _buildTopSection(),
          const SizedBox(height: 24),

          // Middle Section - Today's Workout
          _buildWorkoutSection(),
          const SizedBox(height: 24),

          // Bottom Section - Quick Stats
          _buildQuickStatsSection(),
          const SizedBox(height: 24),

          // Weekly Goals Section
          _buildWeeklyGoalsSection(),
          const SizedBox(height: 24),

          // Weekly Summary
          _buildWeeklySummarySection(),
        ],
      ),
    );
  }

  Widget _buildTopSection() {
    return Row(
      children: [
        Expanded(
          child: StatCard(
            title: "Current Weight",
            value: "${_stats!.currentWeight.toStringAsFixed(1)} kg",
            subtitle: _dashboardService.getWeightChangeDescription(_stats!.weightChange),
            subtitleColor: _stats!.weightChange < 0 ? const Color(0xFF00ff00) : const Color(0xFFff4d4d),
            showTrendingUp: _stats!.weightChange != 0,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: StatCard(
            title: "Streak",
            value: _dashboardService.getStreakText(_stats!.streakDays),
            subtitle: _stats!.streakDays > 0 ? "+1 day" : "Start today!",
            icon: Icons.local_fire_department,
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Today's Workout",
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        WorkoutCard(
          workoutName: _stats!.todayWorkoutName,
          duration: _stats!.todayWorkoutDuration,
          onStartWorkout: _startWorkout,
        ),
      ],
    );
  }

  Widget _buildQuickStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "This Week",
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: "Total Volume",
                value: _dashboardService.formatVolume(_stats!.totalVolumeThisWeek),
                subtitle: "10%",
                showTrendingUp: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: "Calories Burned",
                value: _dashboardService.formatCalories(_stats!.caloriesBurned),
                subtitle: "5%",
                showTrendingUp: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: "Best Lift",
                value: _dashboardService.getBestLiftText(_stats!.bestLift, _stats!.bestLiftWeight),
                subtitle: "2%",
                showTrendingUp: _stats!.bestLiftWeight > 0,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: "Workouts",
                value: "${_stats!.workoutsCompleted}",
                subtitle: "+1 this week",
                showTrendingUp: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWeeklyGoalsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Weekly Goals",
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // Phase Information Card
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1a1a1a),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _weekData?.currentWeek != null && _weekData!.currentWeek % 5 == 0
                  ? const Color(0xFFff6b35) // Orange for deload week
                  : const Color(0xFF00ff00), // Green for normal weeks
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _weekData?.currentWeek != null && _weekData!.currentWeek % 5 == 0
                          ? const Color(0xFFff6b35) // Orange for deload week
                          : const Color(0xFF00ff00), // Green for normal weeks
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      _weekData?.currentPhase.toUpperCase() ?? 'BEGINNER',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${_weekData?.recommendedWorkoutsPerWeek ?? 3} workouts/week • ${_weekData?.recommendedDuration ?? '20-30min'}',
                    style: const TextStyle(
                      color: Color(0xFFa3a3a3),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                _weekData?.weekDisplayText ?? 'Week 1',
                style: TextStyle(
                  color: _weekData?.currentWeek != null && _weekData!.currentWeek % 5 == 0
                      ? const Color(0xFFff6b35) // Orange for deload week
                      : const Color(0xFF00ff00), // Green for normal weeks
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _weekData?.currentWeek != null && _weekData!.currentWeek % 5 == 0
                    ? 'Deload Week - Reduced Volume for Recovery'
                    : 'Current Phase Goals',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Goals Grid
        Column(
          children: [
            // Consistency Goal
            _buildGoalCard(
              icon: Icons.calendar_today,
              title: 'Consistency Goal',
              description: 'Complete 4 workouts this week',
              progress: 2,
              target: 4,
              unit: 'workouts',
              color: const Color(0xFF00ff00),
            ),
            const SizedBox(height: 12),

            // Volume Goal
            _buildGoalCard(
              icon: Icons.fitness_center,
              title: 'Volume Goal',
              description: 'Lift 12,000 lbs total this week',
              progress: 7500,
              target: 12000,
              unit: 'lbs',
              color: const Color(0xFF00bfff),
            ),
            const SizedBox(height: 12),

            // Calorie Goal
            _buildGoalCard(
              icon: Icons.local_fire_department,
              title: 'Calorie Goal',
              description: 'Burn 1,500 calories this week',
              progress: 850,
              target: 1500,
              unit: 'cal',
              color: const Color(0xFFff6b35),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGoalCard({
    required IconData icon,
    required String title,
    required String description,
    required int progress,
    required int target,
    required String unit,
    required Color color,
  }) {
    final progressPercentage = (progress / target).clamp(0.0, 1.0);
    final isCompleted = progress >= target;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1a1a1a),
        borderRadius: BorderRadius.circular(12),
        border: isCompleted
            ? Border.all(color: const Color(0xFF00ff00), width: 1)
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      description,
                      style: const TextStyle(
                        color: Color(0xFFa3a3a3),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (isCompleted)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF00ff00),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.black,
                    size: 16,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress Bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$progress $unit',
                    style: TextStyle(
                      color: color,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${(progressPercentage * 100).round()}%',
                    style: const TextStyle(
                      color: Color(0xFFa3a3a3),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Container(
                height: 6,
                decoration: BoxDecoration(
                  color: const Color(0xFF2a2a2a),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progressPercentage,
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklySummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Weekly Summary",
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Column(
          children: [
            SummaryStatCard(
              title: "Workouts Completed",
              value: "${_stats!.workoutsCompleted}",
              subtitle: "+1 from last week",
            ),
            const SizedBox(height: 12),
            SummaryStatCard(
              title: "Weight Change",
              value: _dashboardService.getWeightChangeText(_stats!.weightChange),
              subtitle: _dashboardService.getWeightChangeDescription(_stats!.weightChange),
              subtitleColor: _stats!.weightChange < 0 ? const Color(0xFF00ff00) : const Color(0xFFff4d4d),
            ),
            const SizedBox(height: 12),
            SummaryStatCard(
              title: "Strength Gains",
              value: _dashboardService.getBestLiftText(_stats!.bestLift, _stats!.bestLiftWeight),
              subtitle: "+5 kg from last week",
            ),
          ],
        ),
      ],
    );
  }


}

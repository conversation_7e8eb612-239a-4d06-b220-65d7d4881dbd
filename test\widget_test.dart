// This is a basic Flutter widget test for the Personal Training App.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:personal_training_app/main.dart';

void main() {
  testWidgets('Personal Training App can be created', (WidgetTester tester) async {
    // Just verify the app can be instantiated
    const app = MyApp();
    expect(app, isNotNull);
  });
}

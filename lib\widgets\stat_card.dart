// Widget for displaying a stat card (e.g., weight, streak)
import 'package:flutter/material.dart';

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData? icon;
  final Color? subtitleColor;
  final bool showTrendingUp;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.subtitleColor,
    this.showTrendingUp = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: const Color(0xFF1a1a1a),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Color(0xFFa3a3a3),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                if (showTrendingUp)
                  const Icon(
                    Icons.trending_up,
                    color: Color(0xFF00ff00),
                    size: 16,
                  ),
                if (icon != null && !showTrendingUp)
                  Icon(
                    icon,
                    color: subtitleColor ?? const Color(0xFF00ff00),
                    size: 16,
                  ),
                if (showTrendingUp || icon != null) const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    subtitle!,
                    style: TextStyle(
                      color: subtitleColor ?? const Color(0xFF00ff00),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

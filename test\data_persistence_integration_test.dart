import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:personal_training_app/models/user_profile.dart';
import 'package:personal_training_app/services/local_storage.dart';

void main() {
  group('Data Persistence Integration Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
      // Reset the singleton instance
      LocalStorageService.resetForTesting();
      await LocalStorageService.init();
    });

    testWidgets('should persist profile data across app sessions', (WidgetTester tester) async {
      final storageService = LocalStorageService();

      // Create and save a profile
      final originalProfile = UserProfile(
        name: '<PERSON>',
        age: 30,
        height: 180.0,
        weight: 75.0,
        weighInDay: 'Monday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );

      await storageService.saveUserProfile(originalProfile);

      // Simulate app restart by creating a new storage service instance
      final newStorageService = LocalStorageService();
      final loadedProfile = await newStorageService.loadUserProfile();

      // Verify the profile was persisted correctly
      expect(loadedProfile, isNotNull);
      expect(loadedProfile!.name, equals('John Doe'));
      expect(loadedProfile.age, equals(30));
      expect(loadedProfile.height, equals(180.0));
      expect(loadedProfile.weight, equals(75.0));
      expect(loadedProfile.weighInDay, equals('Monday'));
      expect(loadedProfile.weightUnit, equals('kg'));
      expect(loadedProfile.heightUnit, equals('cm'));
    });

    testWidgets('should handle profile updates correctly', (WidgetTester tester) async {
      final storageService = LocalStorageService();

      // Create and save initial profile
      final initialProfile = UserProfile(
        name: 'Jane Smith',
        age: 25,
        height: 165.0,
        weight: 60.0,
        weighInDay: 'Sunday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );

      await storageService.saveUserProfile(initialProfile);

      // Update the profile
      final updatedProfile = UserProfile(
        name: 'Jane Smith-Johnson',
        age: 26,
        height: 165.0,
        weight: 58.0,
        weighInDay: 'Wednesday',
        weightUnit: 'lbs',
        heightUnit: 'ft',
      );

      await storageService.saveUserProfile(updatedProfile);

      // Load and verify the updated profile
      final loadedProfile = await storageService.loadUserProfile();

      expect(loadedProfile, isNotNull);
      expect(loadedProfile!.name, equals('Jane Smith-Johnson'));
      expect(loadedProfile.age, equals(26));
      expect(loadedProfile.weight, equals(58.0));
      expect(loadedProfile.weighInDay, equals('Wednesday'));
      expect(loadedProfile.weightUnit, equals('lbs'));
      expect(loadedProfile.heightUnit, equals('ft'));
    });

    testWidgets('should return null when no profile exists', (WidgetTester tester) async {
      final storageService = LocalStorageService();

      // Try to load profile when none exists
      final loadedProfile = await storageService.loadUserProfile();

      expect(loadedProfile, isNull);
    });

    testWidgets('should handle corrupted data gracefully', (WidgetTester tester) async {
      // Set corrupted data in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_profile', 'invalid json data');

      final storageService = LocalStorageService();
      final loadedProfile = await storageService.loadUserProfile();

      // Should return null for corrupted data
      expect(loadedProfile, isNull);
    });

    testWidgets('should preserve unit preferences in calculations', (WidgetTester tester) async {
      final storageService = LocalStorageService();

      // Create profile with imperial units
      final imperialProfile = UserProfile(
        name: 'Imperial User',
        age: 30,
        height: 6.0, // 6 feet
        weight: 180.0, // 180 lbs
        weighInDay: 'Friday',
        weightUnit: 'lbs',
        heightUnit: 'ft',
      );

      await storageService.saveUserProfile(imperialProfile);
      final loadedProfile = await storageService.loadUserProfile();

      expect(loadedProfile, isNotNull);
      expect(loadedProfile!.weightUnit, equals('lbs'));
      expect(loadedProfile.heightUnit, equals('ft'));
      expect(loadedProfile.formattedWeight, equals('180.0 lbs'));
      expect(loadedProfile.formattedHeight, equals('6\'0"'));
      
      // BMI should still be calculated correctly
      expect(loadedProfile.bmi, greaterThan(20.0));
      expect(loadedProfile.bmi, lessThan(30.0));
    });
  });
}

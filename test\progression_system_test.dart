import 'package:flutter_test/flutter_test.dart';
import 'package:personal_training_app/models/exercise.dart';
import 'package:personal_training_app/models/exercise_progression.dart';
import 'package:personal_training_app/models/user_profile.dart';
import 'package:personal_training_app/services/progression_service.dart';
import 'package:personal_training_app/services/week_tracking_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Progression System Tests', () {
    late ProgressionService progressionService;
    late WeekTrackingService weekService;
    late UserProfile testProfile;

    setUp(() {
      progressionService = ProgressionService();
      weekService = WeekTrackingService();
      testProfile = UserProfile(
        name: 'Test User',
        age: 25,
        weight: 70.0,
        height: 175,
        weighInDay: 'Monday',
        weightUnit: 'kg',
        heightUnit: 'cm',
      );
    });

    test('should initialize progression for bodyweight exercise', () {
      final exercise = Exercise(
        id: 'test-pushups',
        name: 'Push-ups',
        category: 'strength',
        icon: 'fitness_center',
        sets: 3,
        reps: 10,
        duration: null,
        targetWeight: null,
        isCompleted: false,
      );

      final progression = progressionService.initializeProgression(exercise);

      expect(progression.exerciseId, equals('test-pushups'));
      expect(progression.exerciseName, equals('Push-ups'));
      expect(progression.type, equals(ExerciseType.bodyweight));
      expect(progression.currentReps, equals(10));
      expect(progression.currentSets, equals(3));
      expect(progression.currentWeight, equals(0.0));
      expect(progression.consecutiveSuccesses, equals(0));
      expect(progression.consecutiveFailures, equals(0));
    });

    test('should initialize progression for dumbbell exercise', () {
      final exercise = Exercise(
        id: 'test-curls',
        name: 'Bicep Curls',
        category: 'strength',
        icon: 'fitness_center',
        sets: 3,
        reps: 12,
        duration: null,
        targetWeight: 15.0,
        isCompleted: false,
      );

      final progression = progressionService.initializeProgression(exercise);

      expect(progression.type, equals(ExerciseType.dumbbell));
      expect(progression.currentWeight, equals(15.0));
      expect(progression.currentReps, equals(12));
    });

    test('should initialize progression for timed hold exercise', () {
      final exercise = Exercise(
        id: 'test-plank',
        name: 'Plank Hold',
        category: 'strength',
        icon: 'timer',
        sets: 3,
        reps: 0, // Use 0 instead of null for timed exercises
        duration: 30,
        targetWeight: null,
        isCompleted: false,
      );

      final progression = progressionService.initializeProgression(exercise);

      expect(progression.type, equals(ExerciseType.timedHold));
      expect(progression.currentDuration, equals(30));
      expect(progression.currentSets, equals(3));
    });

    test('should initialize progression for cardio exercise', () {
      final exercise = Exercise(
        id: 'test-run',
        name: 'Running',
        category: 'cardio',
        icon: 'directions_run',
        sets: 1,
        reps: 0, // Use 0 instead of null for cardio exercises
        duration: 600, // 10 minutes
        targetWeight: null,
        isCompleted: false,
      );

      final progression = progressionService.initializeProgression(exercise);

      expect(progression.type, equals(ExerciseType.cardio));
      expect(progression.currentDuration, equals(600));
    });

    test('should apply success progression for bodyweight exercise', () async {
      final exercise = Exercise(
        id: 'test-pushups',
        name: 'Push-ups',
        category: 'strength',
        icon: 'fitness_center',
        sets: 3,
        reps: 10,
        duration: null,
        targetWeight: null,
        isCompleted: true,
      );

      var progression = progressionService.initializeProgression(exercise);
      
      // First success - should not progress yet
      progression = await progressionService.applyProgression(
        progression: progression,
        allSetsCompleted: true,
        userProfile: testProfile,
      );
      
      expect(progression.consecutiveSuccesses, equals(1));
      expect(progression.currentReps, equals(10)); // No change yet
      
      // Second success - should progress
      progression = await progressionService.applyProgression(
        progression: progression,
        allSetsCompleted: true,
        userProfile: testProfile,
      );
      
      expect(progression.consecutiveSuccesses, equals(0)); // Reset after progression
      expect(progression.currentReps, equals(11)); // Increased by 1
      expect(progression.history.length, equals(1));
      expect(progression.history.first.changeType, equals('progression'));
    });

    test('should apply failure regression for bodyweight exercise', () async {
      final exercise = Exercise(
        id: 'test-pushups',
        name: 'Push-ups',
        category: 'strength',
        icon: 'fitness_center',
        sets: 3,
        reps: 15,
        duration: null,
        targetWeight: null,
        isCompleted: false,
      );

      var progression = progressionService.initializeProgression(exercise);
      progression = progression.copyWith(currentReps: 15);

      // First failure - should not regress yet
      progression = await progressionService.applyProgression(
        progression: progression,
        allSetsCompleted: false,
        userProfile: testProfile,
      );

      expect(progression.consecutiveFailures, equals(1));
      expect(progression.currentReps, equals(15)); // No change yet

      // Second failure - should regress
      progression = await progressionService.applyProgression(
        progression: progression,
        allSetsCompleted: false,
        userProfile: testProfile,
      );

      expect(progression.consecutiveFailures, equals(0)); // Reset after regression
      expect(progression.currentReps, equals(14)); // Decreased by 1
      expect(progression.history.length, equals(1));
      expect(progression.history.first.changeType, equals('failure'));
    });

    test('should apply success progression for dumbbell exercise', () async {
      final exercise = Exercise(
        id: 'test-curls',
        name: 'Bicep Curls',
        category: 'strength',
        icon: 'fitness_center',
        sets: 3,
        reps: 12,
        duration: null,
        targetWeight: 10.0,
        isCompleted: true,
      );

      var progression = progressionService.initializeProgression(exercise);
      progression = progression.copyWith(consecutiveSuccesses: 1); // Simulate one success
      
      // Second success - should progress weight
      progression = await progressionService.applyProgression(
        progression: progression,
        allSetsCompleted: true,
        userProfile: testProfile,
      );
      
      expect(progression.currentWeight, greaterThan(10.0)); // Weight increased
      expect(progression.history.length, equals(1));
      expect(progression.history.first.changeType, equals('progression'));
    });

    test('should calculate calories burned correctly', () async {
      final exercises = [
        Exercise(
          id: 'test-pushups',
          name: 'Push-ups',
          category: 'strength',
          icon: 'fitness_center',
          sets: 3,
          reps: 10,
          duration: null,
          targetWeight: null,
          isCompleted: true,
        ),
        Exercise(
          id: 'test-run',
          name: 'Running',
          category: 'cardio',
          icon: 'directions_run',
          sets: 1,
          reps: 0, // Use 0 instead of null
          duration: 600, // 10 minutes
          targetWeight: null,
          isCompleted: true,
        ),
      ];

      final progressions = <String, ExerciseProgression>{};
      for (final exercise in exercises) {
        progressions[exercise.id] = progressionService.initializeProgression(exercise);
      }

      final calories = await progressionService.calculateWorkoutCalories(
        exercises: exercises,
        progressions: progressions,
        userProfile: testProfile,
        workoutDurationMinutes: 30,
      );

      expect(calories, greaterThan(0));
    });

    test('should track weeks correctly', () async {
      // Reset week tracking for clean test
      await weekService.resetWeekTracking();
      
      final weekData = await weekService.loadWeekData();
      expect(weekData.currentWeek, equals(1));
      expect(weekData.totalWorkoutsCompleted, equals(0));
      
      // Complete a workout
      final updatedWeekData = await weekService.updateAfterWorkout();
      expect(updatedWeekData.totalWorkoutsCompleted, equals(1));
    });

    test('should identify deload weeks correctly', () async {
      await weekService.resetWeekTracking();
      
      // Week 1-4 should not be deload weeks
      expect(await weekService.isDeloadWeek(), isFalse);
      
      // Simulate advancing to week 5
      final weekData = WeekTrackingData(
        currentWeek: 5,
        startDate: DateTime.now().subtract(const Duration(days: 28)),
        lastWorkoutDate: DateTime.now(),
        totalWorkoutsCompleted: 20,
      );
      await weekService.saveWeekData(weekData);
      
      expect(await weekService.isDeloadWeek(), isTrue);
    });

    test('should get weeks until next deload correctly', () async {
      await weekService.resetWeekTracking();
      
      // Week 1: 4 weeks until deload
      expect(await weekService.getWeeksUntilDeload(), equals(4));
      
      // Simulate week 3: 2 weeks until deload
      final weekData = WeekTrackingData(
        currentWeek: 3,
        startDate: DateTime.now().subtract(const Duration(days: 14)),
        lastWorkoutDate: DateTime.now(),
        totalWorkoutsCompleted: 12,
      );
      await weekService.saveWeekData(weekData);
      
      expect(await weekService.getWeeksUntilDeload(), equals(2));
    });
  });
}

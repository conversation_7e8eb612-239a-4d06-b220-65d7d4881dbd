// Exercise model
class Exercise {
  final String id;
  final String name;
  final String category; // 'warmup', 'strength', 'cardio', etc.
  final String icon; // Material icon name
  final int sets;
  final int reps;
  final int? duration; // in seconds, for time-based exercises
  final double? targetWeight; // in kg/lbs
  final String? unit; // 'kg', 'lbs', 'seconds', etc.
  bool isCompleted;

  Exercise({
    required this.id,
    required this.name,
    required this.category,
    required this.icon,
    required this.sets,
    required this.reps,
    this.duration,
    this.targetWeight,
    this.unit,
    this.isCompleted = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'icon': icon,
      'sets': sets,
      'reps': reps,
      'duration': duration,
      'targetWeight': targetWeight,
      'unit': unit,
      'isCompleted': isCompleted,
    };
  }

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      icon: json['icon'],
      sets: json['sets'],
      reps: json['reps'],
      duration: json['duration'],
      targetWeight: json['targetWeight']?.toDouble(),
      unit: json['unit'],
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  Exercise copyWith({
    String? id,
    String? name,
    String? category,
    String? icon,
    int? sets,
    int? reps,
    int? duration,
    double? targetWeight,
    String? unit,
    bool? isCompleted,
  }) {
    return Exercise(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      icon: icon ?? this.icon,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      duration: duration ?? this.duration,
      targetWeight: targetWeight ?? this.targetWeight,
      unit: unit ?? this.unit,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  String get displayText {
    if (duration != null) {
      final minutes = duration! ~/ 60;
      final seconds = duration! % 60;
      if (minutes > 0) {
        return '$sets sets x $minutes:${seconds.toString().padLeft(2, '0')}';
      } else {
        return '$sets sets x $duration seconds';
      }
    }
    return '$sets sets x $reps reps';
  }

  String get targetText {
    if (targetWeight != null && unit != null) {
      return 'Target: ${targetWeight!.toStringAsFixed(0)} $unit';
    }
    return '';
  }
}

import 'package:flutter/material.dart';
import '../services/local_storage.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  Future<void> _checkOnboardingStatus() async {
    try {
      // Add a small delay for better UX (optional)
      await Future.delayed(const Duration(milliseconds: 1500));

      // Check if user profile exists
      final profile = await LocalStorageService().loadUserProfile();

      if (!mounted) return;

      if (profile != null) {
        // User has completed onboarding, go to home
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        // User hasn't completed onboarding, show onboarding
        Navigator.pushReplacementNamed(context, '/onboarding');
      }
    } catch (e) {
      // If there's an error loading profile, show onboarding to be safe
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/onboarding');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.fitness_center,
                size: 60,
                color: Color.fromARGB(255, 225, 133, 5),
              ),
            ),
            const SizedBox(height: 32),
            
            // App Title
            const Text(
              'Personal Trainer',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            
            // Subtitle
            Text(
              'Your fitness journey starts here',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            const SizedBox(height: 48),
            
            // Loading Indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
